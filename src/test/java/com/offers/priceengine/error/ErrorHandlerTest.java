package com.offers.priceengine.error;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.offers.priceengine.configuration.MetricsConfigurator;
import com.offers.priceengine.error.apierror.ApiError;
import com.offers.priceengine.error.standardized.StandardizedError;
import com.offers.priceengine.log.CustomMetricLogConvertor;
import com.offers.priceengine.model.service.price.PriceErrorResponse;
import io.micrometer.core.instrument.config.validate.ValidationException;
import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.servlet.NoHandlerFoundException;

class ErrorHandlerTest {

    @Mock
    private CustomMetricLogConvertor customMetricLogConvertor;

    @Mock
    private ServletWebRequest webRequest;

    @Mock
    private HttpServletRequest httpServletRequest;

    @InjectMocks
    private ErrorHandler errorHandler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(webRequest.getRequest()).thenReturn(httpServletRequest);
        when(httpServletRequest.getRequestURI()).thenReturn("/api/test");
    }

    @Test
    void unsupportedMethod_shouldReturnNotImplemented() {
        // Given
        PricingMethodNotSupportedException exception = new PricingMethodNotSupportedException("Method not supported");

        // When
        ResponseEntity<Object> response = errorHandler.unsupportedMethod(exception, webRequest);

        // Then
        assertEquals(HttpStatus.NOT_IMPLEMENTED, response.getStatusCode());
        verify(customMetricLogConvertor).increment(eq(MetricsConfigurator.RTPE_5XX_METRIC_NAME), any(Map.class));
    }

    @Test
    void badRequest_shouldReturnBadRequest() {
        // Given
        BadRequestException exception = new BadRequestException("Bad request");

        // When
        ResponseEntity<Object> response = errorHandler.badRequest(exception, webRequest);

        // Then
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        verify(customMetricLogConvertor).increment(eq(MetricsConfigurator.RTPE_4XX_METRIC_NAME), any(Map.class));
    }

    @Test
    void notFound_shouldReturnNotFound() {
        // Given
        NotFoundException exception = new NotFoundException("Not found");

        // When
        ResponseEntity<Object> response = errorHandler.notFound(exception, webRequest);

        // Then
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
    }

    @Test
    void internalError_shouldReturnInternalServerError() {
        // Given
        RuntimeException exception = new RuntimeException("Internal error");

        // When
        ResponseEntity<Object> response = errorHandler.internalError(exception, webRequest);

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        verify(customMetricLogConvertor).increment(eq(MetricsConfigurator.RTPE_5XX_METRIC_NAME), any(Map.class));
    }

    @Test
    void priceNotFound_shouldReturnNotFound() {
        // Given
        PriceNotFoundException exception = new PriceNotFoundException("Price not found");

        // When
        ResponseEntity<Object> response = errorHandler.priceNotFound(exception, webRequest);

        // Then
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        verify(customMetricLogConvertor).increment(eq(MetricsConfigurator.RTPE_4XX_METRIC_NAME), any(Map.class));
    }

    @Test
    void basketPriceException_shouldReturnBadRequest() {
        // Given
        BasketPriceException exception = new BasketPriceException(List.of(new PriceErrorResponse()));

        // When
        ResponseEntity<Object> response = errorHandler.promotionsEvaluationPriceError(exception, webRequest);

        // Then
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        verify(customMetricLogConvertor).increment(eq(MetricsConfigurator.RTPE_4XX_METRIC_NAME), any(Map.class));
    }

    @Test
    void priceOutOfRange_shouldReturnBadRequest() {
        // Given
        PriceOutOfRangeException exception = new PriceOutOfRangeException("Price out of range");

        // When
        ResponseEntity<Object> response = errorHandler.priceOutOfRange(exception, webRequest);

        // Then
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        verify(customMetricLogConvertor).increment(eq(MetricsConfigurator.RTPE_4XX_METRIC_NAME), any(Map.class));
    }


    @Test
    void handleMethodArgumentNotValid_shouldReturnBadRequest() {
        // Given
        MethodArgumentNotValidException exception = mock(MethodArgumentNotValidException.class);
        BindingResult bindingResult = mock(BindingResult.class);
        List<FieldError> fieldErrors = new ArrayList<>();
        fieldErrors.add(new FieldError("object", "field", "Field error message"));

        List<ObjectError> globalErrors = new ArrayList<>();
        globalErrors.add(new ObjectError("object", "Global error message"));

        when(exception.getBindingResult()).thenReturn(bindingResult);
        when(bindingResult.getFieldErrors()).thenReturn(fieldErrors);
        when(bindingResult.getGlobalErrors()).thenReturn(globalErrors);
        HttpHeaders headers = new HttpHeaders();

        // When
        ResponseEntity<Object> response = errorHandler.handleMethodArgumentNotValid(
                exception, headers, HttpStatus.BAD_REQUEST, webRequest);

        // Then
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        verify(customMetricLogConvertor).increment(eq(MetricsConfigurator.RTPE_4XX_METRIC_NAME), any(Map.class));
    }

    @Test
    void handleHttpMessageNotReadable_shouldReturnBadRequest() {
        // Given
        HttpMessageNotReadableException exception = new HttpMessageNotReadableException("Message not readable");
        HttpHeaders headers = new HttpHeaders();

        // When
        ResponseEntity<Object> response = errorHandler.handleHttpMessageNotReadable(
                exception, headers, HttpStatus.BAD_REQUEST, webRequest);

        // Then
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        verify(customMetricLogConvertor).increment(eq(MetricsConfigurator.RTPE_4XX_METRIC_NAME), any(Map.class));
    }

    @Test
    void handleHttpMessageNotWritable_shouldReturnInternalServerError() {
        // Given
        HttpMessageNotWritableException exception = new HttpMessageNotWritableException("Message not writable");
        HttpHeaders headers = new HttpHeaders();

        // When
        ResponseEntity<Object> response = errorHandler.handleHttpMessageNotWritable(
                exception, headers, HttpStatus.INTERNAL_SERVER_ERROR, webRequest);

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        verify(customMetricLogConvertor).increment(eq(MetricsConfigurator.RTPE_5XX_METRIC_NAME), any(Map.class));
    }

    @Test
    void isUplevelledRequest_shouldReturnFalseForNonV3Requests() {
        // Given
        String nonV3RequestURI = "/api/v1/test";

        // When
        boolean result = ReflectionTestUtils.invokeMethod(errorHandler, "isUplevelledRequest", nonV3RequestURI);

        // Then
        assertFalse(result);
    }



    @Test
    void buildResponseEntity_withStatusAndException_shouldCreateProperResponse() {
        // Given
        HttpStatus status = HttpStatus.BAD_REQUEST;
        Exception exception = new Exception("Test exception");
        String message = "Test message";
        List<PriceErrorResponse> errors = null;

        // When
        ResponseEntity<Object> response = ReflectionTestUtils.invokeMethod(
                errorHandler, "buildResponseEntity", status, exception, message, webRequest, errors);

        // Then
        assertEquals(status, response.getStatusCode());
        assertTrue(response.getBody() instanceof StandardizedError);
        StandardizedError error = (StandardizedError) response.getBody();
        assertEquals(message, error.getMessage());
    }
}
